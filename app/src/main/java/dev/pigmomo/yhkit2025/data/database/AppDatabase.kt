package dev.pigmomo.yhkit2025.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import dev.pigmomo.yhkit2025.data.dao.ConfigTokenDao
import dev.pigmomo.yhkit2025.data.dao.DeletedOrderDao
import dev.pigmomo.yhkit2025.data.dao.LoginTokenDao
import dev.pigmomo.yhkit2025.data.dao.LogDao
import dev.pigmomo.yhkit2025.data.dao.productmonitor.MonitoringPlanDao
import dev.pigmomo.yhkit2025.data.dao.OrderTokenDao
import dev.pigmomo.yhkit2025.data.dao.PhoneWithPasswordDao
import dev.pigmomo.yhkit2025.data.dao.productmonitor.ProductMonitorDao
import dev.pigmomo.yhkit2025.data.dao.productmonitor.ProductChangeRecordDao
import dev.pigmomo.yhkit2025.data.model.ConfigTokenEntity
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import dev.pigmomo.yhkit2025.data.model.LoginTokenEntity
import dev.pigmomo.yhkit2025.data.model.LogEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.data.model.PhoneWithPasswordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.utils.DateTypeConverter
import dev.pigmomo.yhkit2025.data.utils.StringListConverter
import dev.pigmomo.yhkit2025.data.utils.ProductTypeConverter

/**
 * 应用数据库类
 * 定义数据库结构和提供DAO访问
 */
@Database(
    entities = [
        LogEntity::class,
        LoginTokenEntity::class,
        OrderTokenEntity::class,
        ConfigTokenEntity::class,
        PhoneWithPasswordEntity::class,
        DeletedOrderEntity::class,
        MonitoringPlanEntity::class,
        ProductMonitorEntity::class,
        ProductChangeRecordEntity::class
    ],
    version = 5,
    exportSchema = false
)

@TypeConverters(DateTypeConverter::class, StringListConverter::class, ProductTypeConverter::class)
abstract class AppDatabase : RoomDatabase() {

    /**
     * 获取日志数据访问对象
     */
    abstract fun logDao(): LogDao

    abstract fun configTokenDao(): ConfigTokenDao
    abstract fun loginTokenDao(): LoginTokenDao
    abstract fun orderTokenDao(): OrderTokenDao
    abstract fun phoneWithPasswordDao(): PhoneWithPasswordDao
    abstract fun deletedOrderDao(): DeletedOrderDao
    
    /**
     * 获取监控计划数据访问对象
     */
    abstract fun monitoringPlanDao(): MonitoringPlanDao

    /**
     * 获取商品监控数据访问对象
     */
    abstract fun productMonitorDao(): ProductMonitorDao

    /**
     * 获取商品变化记录数据访问对象
     */
    abstract fun productChangeRecordDao(): ProductChangeRecordDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        /**
         * 获取数据库实例，如果不存在则创建
         * @param context 应用上下文
         * @return 数据库实例
         */
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "yhkit_database"
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
} 