package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 用于展示所有监控商品的详细数据和变化记录
 * @param viewModel 监控数据视图模型
 * @param onNavigateBack 返回导航回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    viewModel: MonitoringDataViewModel,
    onNavigateBack: () -> Unit
) {
    // 从ViewModel获取状态
    val monitoredProducts by viewModel.monitoredProducts.collectAsState()
    val selectedProduct by viewModel.selectedProduct.collectAsState()
    val selectedProductChangeRecords by viewModel.selectedProductChangeRecords.collectAsState()
    val isLoading by viewModel.isLoading
    val errorMessage by viewModel.errorMessage

    // 从ViewModel获取格式化器
    val dateFormat = viewModel.dateFormat
    val fullDateFormat = viewModel.fullDateFormat

    // 显示错误信息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            viewModel.clearErrorMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Monitoring Data") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 显示商品列表
            ProductListScreen(
                products = monitoredProducts,
                viewModel = viewModel,
                modifier = Modifier.padding(paddingValues)
            )
        }
    }
}

/**
 * 商品列表页面
 */
@Composable
fun ProductListScreen(
    products: List<ProductMonitorEntity>,
    viewModel: MonitoringDataViewModel,
    modifier: Modifier = Modifier
) {
    // 管理展开的商品ID（同时只能展开一个）
    var expandedProductId by remember { mutableStateOf<String?>(null) }
    // 管理每个商品的变化记录
    var productChangeRecords by remember { mutableStateOf(mapOf<String, List<ProductChangeRecordEntity>>()) }
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 商品列表
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        items(
            items = products,
            key = { it.id }
        ) { product ->
            val isExpanded = expandedProductId == product.id

            // 其他卡片的可见性动画
            AnimatedVisibility(
                visible = expandedProductId == null || isExpanded,
                enter = fadeIn(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + scaleIn(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    ),
                    initialScale = 0.8f
                ),
                exit = fadeOut(
                    animationSpec = tween(
                        durationMillis = 200,
                        easing = FastOutSlowInEasing
                    )
                ) + scaleOut(
                    animationSpec = tween(
                        durationMillis = 200,
                        easing = FastOutSlowInEasing
                    ),
                    targetScale = 0.8f
                )
            ) {
                ProductListItem(
                    product = product,
                    isExpanded = isExpanded,
                    changeRecords = productChangeRecords[product.id] ?: emptyList(),
                    onToggleExpand = { productId ->
                        if (isExpanded) {
                            // 收起
                            expandedProductId = null
                            productChangeRecords = productChangeRecords - productId
                        } else {
                            // 展开
                            expandedProductId = productId
                            // 加载变化记录
                            coroutineScope.launch {
                                try {
                                    val records = viewModel.getProductChangeRecords(product.id, product.shopId)
                                    records.collect { recordList ->
                                        productChangeRecords = productChangeRecords + (productId to recordList.sortedByDescending { it.changeTime })
                                    }
                                } catch (e: Exception) {
                                    Log.e("ProductListScreen", "Failed to load change records for product ${product.id}", e)
                                }
                            }
                        }
                    },
                    onDeleteProduct = { productId, shopId ->
                        // 删除商品监控记录
                        viewModel.deleteProduct(productId, shopId)
                        // 如果当前展开的是被删除的商品，则收起
                        if (expandedProductId == productId) {
                            expandedProductId = null
                            productChangeRecords = productChangeRecords - productId
                        }
                    },
                    dateFormat = viewModel.dateFormat,
                    fullDateFormat = viewModel.fullDateFormat
                )
            }
        }
    }
}


/**
 * 商品列表项组件
 */
@SuppressLint("DefaultLocale")
@Composable
fun ProductListItem(
    product: ProductMonitorEntity,
    isExpanded: Boolean,
    changeRecords: List<ProductChangeRecordEntity>,
    onToggleExpand: (String) -> Unit,
    onDeleteProduct: (String, String) -> Unit,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat
) {
    // 删除确认对话框状态
    var showDeleteDialog by remember { mutableStateOf(false) }

    // 卡片缩放动画
    val cardScale by animateFloatAsState(
        targetValue = if (isExpanded) 1.02f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "card_scale"
    )

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .graphicsLayer {
                scaleX = cardScale
                scaleY = cardScale
            }
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                )
            )
    ) {
        Column {
            // 主要信息区域 - 点击展开/收起
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 商品标题
                Text(
                    text = product.title,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    modifier = Modifier
                        .weight(1f)
                        .horizontalScroll(rememberScrollState())
                )

                // 操作按钮区域
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 删除按钮
                    IconButton(
                        onClick = { showDeleteDialog = true },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除监控",
                            tint = Color(0xFFF44336),
                            modifier = Modifier.size(18.dp)
                        )
                    }

                    // 展开图标（带动画）
                    val rotationAngle by animateFloatAsState(
                        targetValue = if (isExpanded) 180f else 0f,
                        animationSpec = tween(
                            durationMillis = 300,
                            easing = FastOutSlowInEasing
                        ),
                        label = "arrow_rotation"
                    )

                    IconButton(
                        onClick = { onToggleExpand(product.id) },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = "展开详情",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier
                                .size(18.dp)
                                .rotate(rotationAngle)
                        )
                    }
                }
            }

            // SKU、库存、店铺信息行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 2.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = "SKU: ${product.id} ${if (product.originalSkuCode.isNotEmpty() && product.originalSkuCode != product.id) "(${product.originalSkuCode})" else ""}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.alignByBaseline()
                )
                Text(
                    text = "库存: ${if (product.stockNum > 0) "${product.stockNum / 100}" else "未知"}",
                    fontSize = 11.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.alignByBaseline()
                )
                if (product.shopId.isNotEmpty()) {
                    Text(
                        text = "店铺: ${product.shopId}",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.alignByBaseline()
                    )
                }
            }

            // 价格和状态标签行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 价格信息
                if (product.currentPrice > 0) {
                    Text(
                        text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                } else {
                    Text(
                        text = "暂无价格",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // 状态标签组
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 秒杀标签
                    if (product.isSeckill == 1) {
                        Surface(
                            color = Color(0xFFFF9800),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "秒杀",
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }

                    // 可用性状态标签
                    when {
                        product.available == 0 -> {
                            Surface(
                                color = Color(0xFF9E9E9E),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "下架",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.canNotBuy -> {
                            Surface(
                                color = Color(0xFFF44336),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "不可购买",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.stockNum == 0 -> {
                            Surface(
                                color = Color(0xFFF44336),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "缺货",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }

                        product.available == 1 && product.stockNum > 0 -> {
                            Surface(
                                color = Color(0xFF4CAF50),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "有货",
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 10.sp,
                                    modifier = Modifier.padding(horizontal = 6.dp)
                                )
                            }
                        }
                    }

                    // 限购标签
                    if (product.restrictLimit > 0) {
                        Surface(
                            color = Color(0xFFFF9800),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = "限购${product.restrictLimit/100}",
                                color = Color.White,
                                fontSize = 10.sp,
                                lineHeight = 10.sp,
                                modifier = Modifier.padding(horizontal = 6.dp)
                            )
                        }
                    }
                }
            }

            // 展开的详细信息（平滑展开动画）
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeIn(
                    animationSpec = tween(
                        durationMillis = 400,
                        easing = FastOutSlowInEasing
                    )
                ),
                exit = shrinkVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessHigh
                    )
                ) + fadeOut(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 分隔线
                    HorizontalDivider(
                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                        thickness = 1.dp
                    )

                    // 监控状态信息
                    Surface(
                        color = if (product.isMonitoringEnabled) Color(0xFF4CAF50).copy(alpha = 0.1f) else Color(0xFFF44336).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = if (product.isMonitoringEnabled) "监控中 - 上次更新: ${dateFormat.format(product.lastUpdateTime)}" else "已停止 - 上次更新: ${dateFormat.format(product.lastUpdateTime)}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (product.isMonitoringEnabled) Color(0xFF4CAF50) else Color(0xFFF44336),
                            modifier = Modifier.padding(12.dp)
                        )
                    }

                    // 变化记录标题和数量
                    if (changeRecords.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "监控记录",
                                style = MaterialTheme.typography.titleSmall,
                                fontWeight = FontWeight.Bold
                            )
                            Surface(
                                color = Color(0xFF2196F3).copy(alpha = 0.1f),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                Text(
                                    text = "${changeRecords.size}条记录",
                                    style = MaterialTheme.typography.labelMedium,
                                    color = Color(0xFF2196F3),
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                        }

                        // 变化记录列表（带交错动画，限制高度）
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 300.dp), // 限制最大高度为300dp
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            itemsIndexed(changeRecords) { index, record ->
                                AnimatedVisibility(
                                    visible = true,
                                    enter = slideInVertically(
                                        animationSpec = tween(
                                            durationMillis = 300,
                                            delayMillis = index * 50,
                                            easing = FastOutSlowInEasing
                                        ),
                                        initialOffsetY = { it / 4 }
                                    ) + fadeIn(
                                        animationSpec = tween(
                                            durationMillis = 300,
                                            delayMillis = index * 50,
                                            easing = FastOutSlowInEasing
                                        )
                                    )
                                ) {
                                    ChangeRecordCompactItem(
                                        record = record,
                                        dateFormat = fullDateFormat
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("确认删除") },
            text = {
                Text("确定要删除商品「${product.title}」的监控记录吗？\n\n此操作将删除该商品的所有监控数据和变化记录，且无法恢复。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        onDeleteProduct(product.id, product.shopId)
                    }
                ) {
                    Text("删除", color = Color(0xFFF44336))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 紧凑的变化记录项组件
 */
@Composable
fun ChangeRecordCompactItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 变化类型标签
            Surface(
                color = when (record.changeType) {
                    ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                    ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                    ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                    ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                    ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                    ProductChangeType.RESTRICT_CHANGE -> Color(0xFFFFCDD2)
                    else -> Color(0xFF607D8B)
                },
                shape = RoundedCornerShape(4.dp)
            ) {
                Text(
                    text = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> "价格"
                        ProductChangeType.STOCK_CHANGE -> "库存"
                        ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                        ProductChangeType.INFO_CHANGE -> "信息"
                        ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                        ProductChangeType.RESTRICT_CHANGE -> "限购"
                        else -> "其他"
                    },
                    color = Color.White,
                    fontSize = 8.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
                )
            }

            // 变化内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                    Text(
                        text = "${record.oldValue} → ${record.newValue}",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.primary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontWeight = FontWeight.Medium
                    )
                } else if (record.changeDescription.isNotEmpty()) {
                    Text(
                        text = record.changeDescription,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }

        // 时间
        Text(
            text = dateFormat.format(record.changeTime),
            fontSize = 9.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
